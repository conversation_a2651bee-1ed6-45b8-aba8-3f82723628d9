<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SphResource\Pages;
use App\Models\Item;
use App\Models\Pelanggan;
use App\Models\Sph;
use App\Models\LetterSetting;

use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

use Illuminate\Support\HtmlString;

// --- Importing all components for cleaner code ---
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Toggle;

// support
use App\Support\Formatter; 

class SphResource extends Resource
{
    protected static ?string $model = Sph::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Sales';
    protected static ?string $navigationLabel = 'SPH (Penawaran)';

    public static function form(Form $form): Form
    {
        $currencyPrefix = function (Get $get): string {
            // Get the state of the letter_setting_id field.
            // When inside a repeater, we need to traverse up the state path using '../'.
            // The '??' provides a fallback to check the current level, for use outside the repeater.
            $settingId = $get('../../letter_setting_id') ?? $get('letter_setting_id');

            if ($settingId) {
                $setting = LetterSetting::find($settingId);
                return $setting?->locale === 'en' ? '$' : 'Rp';
            }
            return 'Rp';
        };

        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Informasi SPH & Pelanggan')
                            ->schema([
                                Select::make('letter_setting_id')
                                    ->label('Penerbitan Surat')
                                    ->relationship('letterSetting', 'name', fn(Builder $query) => $query->where('is_active', true))
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live(),

                                TextInput::make('sph_number')
                                    ->label('Nomor SPH')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->helperText('Nomor SPH akan dibuat otomatis.'),

                                DatePicker::make('sph_date')
                                    ->label('Tanggal SPH')
                                    ->default(now())
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(fn(Set $set, ?string $state) => $set('valid_until_date', $state ? Carbon::parse($state)->addDays(7)->toDateString() : null)),

                                DatePicker::make('valid_until_date')
                                    ->label('Berlaku Hingga')
                                    ->default(now()->addDays(7))
                                    ->required(),

                                Select::make('customer_id')
                                    ->relationship('customer', 'nama', fn(Builder $query) => $query->orderBy('nama', 'asc'))
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->label('Pelanggan')
                                    ->reactive()
                                    ->afterStateUpdated(function (Set $set, ?string $state) {
                                        $customer = Pelanggan::find($state);
                                        $set('opsional_pic', $customer?->pic_nama);
                                    })
                                    ->columnSpan(['lg' => 2]), // Takes up 2/3 of the width on large screens

                                TextInput::make('opsional_pic')
                                    ->label('Contact Person (U.p.)')
                                    ->helperText('Isi jika berbeda dari PIC utama pelanggan.'),

                            ])
                            ->columns(['lg' => 3])
                            ->collapsible(),

                        Section::make('Detail Item Penawaran')
                            ->schema([
                                Repeater::make('details')
                                    ->label('Item Penawaran')
                                    ->schema([
                                        Grid::make(['default' => 1, 'lg' => 12])
                                            ->schema([
                                                Select::make('item_id')->label('Item/Produk')->options(Item::query()->orderBy('name', 'asc')->pluck('name', 'id'))->searchable()->preload()->required()->columnSpan(['lg' => 8]),
                                                TextInput::make('quantity')->label('Kuantitas')->numeric()->required()->default(1)->live(onBlur: true)->columnSpan(['lg' => 4]),
                                                TextInput::make('description')->label('Deskripsi Tambahan')->columnSpanFull(),

                                                Fieldset::make('Rincian Harga per Unit')
                                                    ->schema([
                                                        // --- UPDATED: Pass the dynamic prefix to the helper function ---
                                                        ...self::getCalculationFields($currencyPrefix),
                                                    ])->columns(3),

                                                Grid::make(2)->schema([
                                                    TextInput::make('price')->label('Harga Jual (per Unit)')->numeric()->prefix($currencyPrefix)->readOnly()->dehydrated(),
                                                    TextInput::make('subtotal')->label('Subtotal')->numeric()->prefix($currencyPrefix)->readOnly(),
                                                ])->columnSpanFull(),
                                            ]),
                                    ])
                                    ->addActionLabel('Tambah Item')
                                    ->collapsible()
                                    ->live(),
                            ])->collapsible(),
                    ])->columnSpan(['lg' => 2]),

                Group::make()
                    ->schema([
                        Section::make('Total Penawaran')
                            ->schema([
                                Placeholder::make('grand_total')
                                    ->label('')
                                    ->content(function (Get $get) use ($currencyPrefix): HtmlString {
                                        $total = collect($get('details'))->sum(function ($item) {
                                            $price = ($item['harga_dasar'] ?? 0) + ($item['ppn'] ?? 0) + ($item['oat'] ?? 0) + ($item['pbbkb'] ?? 0);
                                            return ($item['quantity'] ?? 0) * $price;
                                        });
                                        $currency = $currencyPrefix($get);
                                        $formattedTotal = $currency . ' ' . number_format($total, 2, ',', '.');
                                        return new HtmlString("<span class=\"text-2xl font-bold text-gray-700 dark:text-gray-200\">{$formattedTotal}</span>");
                                    }),
                            ])->collapsible(),

                        Section::make('Dokumen & Catatan')
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('dokumen_sph')
                                    ->label('Unggah Dokumen')
                                    ->collection('dokumen_sph')
                                    ->multiple()
                                    ->maxSize(10240),
                                Textarea::make('terms_and_conditions')
                                    ->label('Syarat dan Ketentuan')
                                    ->rows(4),
                                Textarea::make('notes_internal')
                                    ->label('Catatan Internal')
                                    ->rows(3),
                            ])->collapsible(),

                    ])->columnSpan(['lg' => 1]),
            ])->columns(['lg' => 3]);
    }

    /**
     * Helper function to return the calculation fields for the repeater.
     * --- UPDATED: This function now accepts the currency prefix closure ---
     */
    protected static function getCalculationFields(callable $currencyPrefix): array
    {
        $updateTotals = function (Get $get, Set $set) {
            $price = (float)($get('harga_dasar') ?? 0)
                + (float)($get('ppn') ?? 0)
                + (float)($get('oat') ?? 0)
                + (float)($get('pbbkb') ?? 0);
            $quantity = (float)($get('quantity') ?? 0);
            $set('price', $price);
            $set('subtotal', $price * $quantity);
        };

        return [
            // Price input fields
            TextInput::make('harga_dasar')->label('Harga Dasar')->numeric()->prefix($currencyPrefix)->required()->live(onBlur: true)->afterStateUpdated($updateTotals)->columnSpan(1),
            TextInput::make('ppn')->label('PPN')->numeric()->prefix($currencyPrefix)->required()->live(onBlur: true)->afterStateUpdated($updateTotals)->columnSpan(1),
            TextInput::make('oat')->label('OAT')->numeric()->prefix($currencyPrefix)->required()->live(onBlur: true)->afterStateUpdated($updateTotals)->columnSpan(1),
            TextInput::make('pbbkb')->label('PBBKB')->numeric()->prefix($currencyPrefix)->required()->live(onBlur: true)->afterStateUpdated($updateTotals)->columnSpan(1),

            // Display toggle fields
            Toggle::make('show_ppn')->label('Tampilkan PPN')->default(true)->columnSpan(1),
            Toggle::make('show_oat')->label('Tampilkan OAT')->default(true)->columnSpan(1),
            Toggle::make('show_pbbkb')->label('Tampilkan PBBKB')->default(true)->columnSpan(1),
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sph_number')->label('No. SPH')->searchable()->weight('bold'),
                Tables\Columns\TextColumn::make('customer.nama')->label('Pelanggan')->searchable(),
                Tables\Columns\TextColumn::make('status')->label('Status')->badge()
                    ->color(fn(Sph $record) => $record->status_color)
                    ->formatStateUsing(fn(Sph $record) => $record->status_label),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Total Penawaran')
                    ->formatStateUsing(fn (Sph $record, $state): string => 
                        Formatter::currency($state, $record->letterSetting?->locale ?? 'id')
                    ),
                Tables\Columns\TextColumn::make('sph_date')->label('Tanggal SPH')->date('d M Y'),
                Tables\Columns\TextColumn::make('createdBy.name')->label('Dibuat Oleh'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')->label('Status')->options([
                    'draft' => 'Draft',
                    'pending_approval' => 'Menunggu Approval',
                    'sent' => 'Terkirim',
                    'accepted' => 'Diterima',
                    'rejected' => 'Ditolak',
                    'expired' => 'Kadaluarsa',
                ])->multiple(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('download_pdf')
                    ->label('Download PDF')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function (Sph $record) {
                        try {
                            $sph = Sph::with([
                                'customer',
                                'details.item',
                                'createdBy'
                            ])->find($record->id);

                            $filename = 'SPH_' . str_replace(['/', '\\'], '_', $sph->sph_number) . '_' . now()->format('Ymd_His') . '.pdf';

                            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('sph.sph-pdf', ['record' => $sph])
                                ->setPaper('a4', 'portrait')
                                ->setOptions([
                                    'isHtml5ParserEnabled' => true,
                                    'isPhpEnabled' => true,
                                    'defaultFont' => 'Arial',
                                    'isRemoteEnabled' => true,
                                ]);

                            return response()->streamDownload(function () use ($pdf) {
                                echo $pdf->output();
                            }, $filename, [
                                'Content-Type' => 'application/pdf',
                                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                            ]);
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('Failed to generate SPH PDF: ' . $e->getMessage());

                            \Filament\Notifications\Notification::make()
                                ->title('Error generating PDF')
                                ->body('Failed to generate PDF: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            return null;
                        }
                    }),
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()])])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSphs::route('/'),
            'create' => Pages\CreateSph::route('/create'),
            'edit' => Pages\EditSph::route('/{record}/edit'),
            'view' => Pages\ViewSph::route('/{record}'),
        ];
    }
}
