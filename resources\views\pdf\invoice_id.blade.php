<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ $record->nomor_invoice }}</title>
    <style>
        @page {
            size: A4;
            margin: 0.5in;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .invoice-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-tagline {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .company-contact {
            font-size: 8px;
            margin-top: 5px;
        }

        .invoice-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
            min-height: 60px;
        }

        .invoice-label {
            font-size: 9px;
            margin-bottom: 5px;
        }

        .logo-placeholder {
            border: 1px solid #000;
            width: 80px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            font-size: 10px;
        }

        .detail-left,
        .detail-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .detail-table {
            width: 100%;
        }

        .detail-table td {
            padding: 2px 5px;
            border: none;
        }

        .detail-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .detail-table td:nth-child(2) {
            width: 5%;
            text-align: center;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #000;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }

        .items-table td {
            min-height: 25px;
        }

        .totals-section {
            float: right;
            width: 300px;
            margin-bottom: 20px;
        }

        .totals-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }

        .totals-table td {
            border: 1px solid #000;
            padding: 5px 8px;
        }

        .totals-table td:first-child {
            font-weight: bold;
            background-color: #f0f0f0;
            text-align: right;
        }

        .totals-table td:last-child {
            text-align: right;
        }

        .total-final {
            background-color: #000 !important;
            color: white !important;
            font-weight: bold !important;
        }

        .notes-section {
            clear: both;
            margin-top: 20px;
            font-size: 10px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notes-content {
            border: 1px solid #000;
            padding: 10px;
            min-height: 40px;
        }

        .signature-section {
            margin-top: 30px;
            text-align: right;
            font-size: 10px;
        }

        .signature-box {
            display: inline-block;
            text-align: center;
            width: 200px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .signature-space {
            height: 60px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            margin-top: 5px;
        }

        .page-break {
            page-break-after: always;
        }

        /* Footer Section */
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 10px;
        }

        .footer table {
            width: 100%;
            border-collapse: collapse;
        }

        .footer td {
            vertical-align: top;
            padding: 5px;
        }

        .footer .center {
            text-align: center;
        }

        .footer .right {
            text-align: right;
        }

        .footer p {
            margin: 2px 0;
            line-height: 1.3;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                @if (isset($logoBase64) && !empty($logoBase64))
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo" width="80" height="60">
                @else
                    <div class="logo-placeholder">
                        LOGO<br>PERUSAHAAN
                    </div>
                @endif
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">MITRA TERPERCAYA & HANDAL</div>
                <div class="company-tagline">Agen BBM - Transportasi BBM - Layanan Bunker</div>
                <div class="company-contact">
                    @if ($record->letterSetting)
                        {{ $record->letterSetting->phone_number ?? '0761-22369' }} -
                        {{ $record->letterSetting->email ?? '<EMAIL>' }}<br>
                        {{ $record->letterSetting->website ?? 'www.lintasriauprima.com' }}
                    @else
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    @endif
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-box">
                    <div class="invoice-label">No. Invoice:</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        {{ $record->nomor_invoice ?? 'N/A' }}
                    </div>
                    <div style="margin-top: 5px;">
                        <div class="invoice-label">Status:</div>
                        <div style="font-weight: bold;">
                            {{ ucfirst($record->status ?? 'draft') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        FAKTUR PENJUALAN / INVOICE
    </div>

    <!-- Customer Details -->
    <div class="customer-details">
        <div style="margin-bottom: 15px;">
            <div class="detail-row">
                <span class="detail-label">Nama Pelanggan</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Alamat Pelanggan</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamatUtama?->alamat ?? 'N/A') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">No Surat Pengantar Pengiriman Pertamina</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $record->transaksiPenjualan?->kode ?? 'nomor so' }}</span>
            </div>
            @php
                $deliveryOrders = $record->transaksiPenjualan?->deliveryOrders ?? collect();
                $doNumbers = $deliveryOrders->pluck('kode')->filter()->implode(', ');
            @endphp
            <div class="detail-row">
                <span class="detail-label">No Tanda Bukti</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $doNumbers ?: '15090, 15091' }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Pengiriman Barang</span>
                <span class="detail-colon">:</span>
                <span class="detail-value"></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">No PO</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $record->transaksiPenjualan?->nomor_po ?? '' }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Tanggal PO</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->transaksiPenjualan?->tanggal ? $record->transaksiPenjualan->tanggal->format('Y-m-d') : '' }}</span>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 35%;">Perincian</th>
                <th style="width: 15%;">Harga Satuan</th>
                <th style="width: 15%;">Volume</th>
                <th style="width: 15%;">PPN</th>
                <th style="width: 15%;">Jumlah</th>
            </tr>
        </thead>
        <tbody>
            @php
                $itemNumber = 1;
                $invoiceItems = $record->invoiceItems ?? collect();
                $hasInvoiceItems = $invoiceItems->isNotEmpty();

                // Calculate totals from invoice items
                $totalPenjualan = 0;
                $totalPajak = 0;
            @endphp

            @if ($hasInvoiceItems)
                @foreach ($invoiceItems as $item)
                    {{-- Main item row --}}
                    <tr>
                        <td>{{ $itemNumber++ }}</td>
                        <td class="text-left">
                            {{ $item->item_name ?? ($item->item?->name ?? 'Item tidak ditemukan') }}<br>
                            <small
                                style="color: #6b7280;">{{ $item->item_description ?? ($item->item?->description ?? '') }}</small>
                        </td>
                        <td class="text-right">Rp {{ number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                        <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                            {{ $item->unit ?? 'Liter' }}</td>
                        <td class="text-right">Rp {{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                        <td class="text-right">Rp
                            {{ number_format(($item->unit_price ?? 0) * ($item->quantity ?? 0), 0, ',', '.') }}</td>
                    </tr>
                    @php
                        $totalPenjualan += ($item->unit_price ?? 0) * ($item->quantity ?? 0);
                    @endphp

                    {{-- Operational cost row (if included) --}}
                    @if (($item->operasional_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">Biaya Operasional dikali
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }} liter</td>
                            <td class="text-right">Rp
                                {{ number_format(($item->operasional_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->operasional_amount ?? 0, 0, ',', '.') }}
                            </td>
                        </tr>
                        @php
                            $totalPenjualan += $item->operasional_amount ?? 0;
                        @endphp
                    @endif

                    {{-- PBBKB cost row (if included) --}}
                    @if (($item->pbbkb_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">PBBKB</td>
                            <td class="text-right">Rp
                                {{ number_format(($item->pbbkb_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->pbbkb_amount ?? 0, 0, ',', '.') }}</td>
                        </tr>
                        @php
                            $totalPenjualan += $item->pbbkb_amount ?? 0;
                        @endphp
                    @endif

                    {{-- PPN row --}}
                    @if (($item->ppn_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">PPN</td>
                            <td class="text-right"></td>
                            <td class="text-right"></td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                        </tr>
                        @php
                            $totalPajak += $item->ppn_amount ?? 0;
                        @endphp
                    @endif
                @endforeach
            @else
                @if ($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails)
                    @foreach ($record->transaksiPenjualan->penjualanDetails as $detail)
                        @php
                            $total = $detail->volume_item * $detail->harga_jual;
                            $subtotalCalculated += $total;
                        @endphp
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td style="text-align: left; padding-left: 8px;">
                                {{ $detail->item->name ?? 'N/A' }}
                            </td>
                            <td>{{ number_format($detail->volume_item ?? 0, 0, ',', '.') }} L</td>
                            <td>{{ $record->formatCurrency($detail->harga_jual ?? 0) }}</td>
                            <td>{{ $record->formatCurrency($total) }}</td>
                        </tr>
                    @endforeach
                @endif
            @endif

            <!-- Totals within table -->
            <tr class="totals-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Penjualan</td>
                <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($totalPenjualan, 0, ',', '.') }}
                </td>
            </tr>

            <tr class="totals-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Pajak</td>
                <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($totalPajak, 0, ',', '.') }}
                </td>
            </tr>

            <tr class="final-total-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Invoice Termasuk Pajak</td>
                <td class="text-right" style="font-weight: bold;">Rp.
                    {{ number_format($totalPenjualan + $totalPajak, 0, ',', '.') }}</td>
            </tr>
        </tbody>
    </table>

    <!-- Terbilang Section -->
    <div class="terbilang-section">
        <strong>Terbilang :</strong>
        @php
            $totalInvoice = $totalPenjualan + $totalPajak;
            // Simple number to words conversion for Indonesian
            $terbilang = $record->numberToWords($totalInvoice) ?? 'Dua Juta Empat Ratus Enam Puluh Ribu rupiah';
        @endphp
        "{{ $terbilang }}"
    </div>

    <!-- Payment Notes -->
    <div class="payment-notes">
        1. Payment transfer to account<br>
        2. After Payment please Call or Email transfer from to 0761-22369 or <EMAIL>
    </div>
    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-location">Pekanbaru,
                {{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d F Y') : now()->format('d F Y') }}
            </div>
            <div class="signature-space" style="height: 60px;"></div>
            <div class="signature-name"><strong>Agustiawan Syahputra</strong></div>
            <div class="signature-title">Direktur Utama</div>
        </div>
    </div>

    {{-- Footer Section --}}
    <div class="footer">
        <table>
            <tr>
                <td style="width: 33%;" class="iso-logos">
                    @php
                        $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
                        $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
                            ->whereIn('name', $isoNamesToDisplay)
                            ->get();
                    @endphp

                    @foreach ($isoCertifications as $cert)
                        @php
                            $logoPath = public_path('storage/' . $cert->logo_path);
                        @endphp
                        @if (file_exists($logoPath))
                            <img src="data:image/jpeg;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                                alt="{{ $cert->name }}" style="height: 40px; margin-right: 10px;">
                        @endif
                    @endforeach
                </td>
                <td style="width: 34%;" class="center">
                    <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                    @if ($record->letterSetting)
                        <p>{{ $record->letterSetting->address }}</p>
                        <p>{{ $record->letterSetting->city }}, {{ $record->letterSetting->province }}.
                            {{ $record->letterSetting->postal_code }}</p>
                    @else
                        <p>Jl. Mesjid Al Furqon No. 26</p>
                        <p>Pekanbaru, Riau. 28144</p>
                    @endif
                </td>
                <td style="width: 33%;" class="right">
                    @if ($record->letterSetting)
                        <p>Tel: {{ $record->letterSetting->phone_number }}</p>
                        <p>Email: {{ $record->letterSetting->email }}</p>
                        <p>Web: {{ $record->letterSetting->website }}</p>
                    @else
                        <p>Tel: 0761-22369</p>
                        <p>Email: <EMAIL></p>
                        <p>Web: www.lintasriauprima.com</p>
                    @endif
                </td>
            </tr>
        </table>
    </div>
    </div>
</body>

</html>
