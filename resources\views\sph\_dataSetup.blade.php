@php
    // The $record variable is passed in from the View::make() call.

    // --- UPDATED: The query now specifically fetches only the two required ISOs ---
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();


    // SIGNATURE SET PART
    $signer = null;
    
    // First, check if there is a final, approved record.
    $finalApproval = $record->approvedApprovals; // This uses the HasOne relationship

    if ($finalApproval) {
        // If an approved record exists, the signer is the user who approved it.
        $signer = $finalApproval->user;
    } else {
        // If not yet approved, find the default manager from the notification settings.
        $eventName = 'sph_manager_update_sales'; // The event name for the SPH manager
        $defaultApproverSetting = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName)->first();
        
        if ($defaultApproverSetting) {
            $signer = $defaultApproverSetting->user;
        } 
    }
@endphp
