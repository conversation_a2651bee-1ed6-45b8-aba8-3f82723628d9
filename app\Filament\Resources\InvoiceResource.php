<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InvoiceResource\Pages;
use App\Models\Invoice;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Filament\Notifications\Notification;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?string $navigationLabel = 'Invoice';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Invoice')
                    ->schema([
                        Forms\Components\TextInput::make('nomor_invoice')
                            ->label('Nomor Invoice')
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Nomor invoice akan di-generate otomatis')
                            ->placeholder('Auto-generate saat simpan'),

                        Forms\Components\Select::make('id_transaksi')
                            ->label('Transaksi Penjualan')
                            ->placeholder('Pilih Transaksi Penjualan')
                            ->relationship('transaksiPenjualan', 'id')
                            ->getOptionLabelFromRecordUsing(fn($record) => $record->nomor_transaksi . ' - ' . $record->pelanggan->nama)
                            ->searchable(['id'])
                            ->preload()
                            ->required()
                            // Auto-populated from URL parameter
                            ->default(function () {
                                return request()->query('id_transaksi', null);
                            })
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                if ($state) {
                                    static::populateFromTransaksiPenjualan($set, $get, $state);
                                }
                            })
                            ->afterStateHydrated(function (callable $set, callable $get, $state) {
                                // Auto-populate when form loads with default value from URL
                                if ($state) {
                                    // Set flag for JavaScript to trigger populate after form is ready
                                    $set('_auto_populate_needed', $state);
                                }
                            })
                            ->helperText('Pilih transaksi penjualan untuk auto-populate data'),

                        Forms\Components\Select::make('letter_setting_id')
                            ->label('Penerbitan Surat')
                            ->relationship('letterSetting', 'name', fn($query) => $query->where('is_active', true))
                            ->searchable()
                            ->preload()
                            ->helperText('Format surat akan diambil otomatis dari SPH atau menggunakan default')
                            ->required(),

                        // Trigger calculation when form loads
                        Forms\Components\Hidden::make('_calculation_trigger')
                            ->default('trigger')
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                if ($state === 'trigger') {
                                    // Trigger initial calculations
                                    static::updateTotals($set, $get);
                                    $set('_calculation_trigger', null);
                                }
                            }),

                        Forms\Components\DateTimePicker::make('tanggal_invoice')
                            ->label('Tanggal Invoice')
                            ->required()
                            ->default(now()),

                        Forms\Components\DateTimePicker::make('tanggal_jatuh_tempo')
                            ->label('Tanggal Jatuh Tempo')
                            ->required()
                            ->default(fn() => now()->addDays(30)),
                    ])->columns(2),

                Forms\Components\Section::make('Informasi Pelanggan')
                    ->schema([
                        Forms\Components\TextInput::make('nama_pelanggan')
                            ->label('Nama Pelanggan')
                            ->required()
                            ->dehydrated()
                            ->live()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),

                        Forms\Components\Textarea::make('alamat_pelanggan')
                            ->label('Alamat Pelanggan')

                            ->dehydrated()
                            ->live()
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),

                        Forms\Components\TextInput::make('npwp_pelanggan')
                            ->label('NPWP Pelanggan')
                            ->dehydrated()
                            ->live()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),


                    ])->columns(2),

                Forms\Components\Section::make('Detail Item Transaksi')
                    ->schema([
                        Forms\Components\Placeholder::make('item_details_info')
                            ->label('')
                            ->live()
                            ->content(function (callable $get) {
                                $transaksiId = $get('id_transaksi');
                                if (!$transaksiId) {
                                    return 'Pilih Transaksi Penjualan untuk melihat detail item';
                                }

                                try {
                                    $transaksi = \App\Models\TransaksiPenjualan::with([
                                        'penjualanDetails.item'
                                    ])->find($transaksiId);

                                    if (!$transaksi) {
                                        return 'Data transaksi tidak ditemukan';
                                    }

                                    $details = $transaksi->penjualanDetails;
                                    if ($details->isEmpty()) {
                                        return 'Tidak ada detail item dalam transaksi';
                                    }

                                    $html = '<div class="space-y-2">';
                                    $totalSubtotal = 0;
                                    $totalVolume = 0;

                                    foreach ($details as $detail) {
                                        $itemName = $detail->item->name ?? 'Unknown Item';
                                        $volume = number_format($detail->volume_item, 0, ',', '.');
                                        $harga = number_format($detail->harga_jual, 0, ',', '.');
                                        $subtotal = $detail->volume_item * $detail->harga_jual;
                                        $subtotalFormatted = number_format($subtotal, 0, ',', '.');

                                        $totalSubtotal += $subtotal;
                                        $totalVolume += $detail->volume_item;

                                        $html .= '<div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">';
                                        $html .= '<div>';
                                        $html .= '<div class="font-medium text-gray-900">' . $itemName . '</div>';
                                        $html .= '<div class="text-sm text-gray-600">' . $volume . ' liter × Rp ' . $harga . '</div>';
                                        $html .= '</div>';
                                        $html .= '<div class="text-right">';
                                        $html .= '<div class="font-medium text-gray-900">Rp ' . $subtotalFormatted . '</div>';
                                        $html .= '</div>';
                                        $html .= '</div>';
                                    }

                                    $html .= '<div class="border-t pt-2 mt-2">';
                                    $html .= '<div class="flex justify-between items-center font-bold text-lg">';
                                    $html .= '<span>Total (' . number_format($totalVolume, 0, ',', '.') . ' liter)</span>';
                                    $html .= '<span>Rp ' . number_format($totalSubtotal, 0, ',', '.') . '</span>';
                                    $html .= '</div>';
                                    $html .= '</div>';
                                    $html .= '</div>';

                                    return new \Illuminate\Support\HtmlString($html);
                                } catch (\Exception $e) {
                                    return 'Error loading item details: ' . $e->getMessage();
                                }
                            })
                            ->columnSpanFull(),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Detail Item & Perhitungan Biaya')
                    ->schema([
                        Forms\Components\Repeater::make('invoiceItems')
                            ->label('Item Invoice')
                            ->relationship('invoiceItems')
                            ->schema([
                                Forms\Components\Grid::make(4)
                                    ->schema([
                                        Forms\Components\Select::make('item_id')
                                            ->label('Item')
                                            ->relationship('item', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                                if ($state) {
                                                    $item = \App\Models\Item::find($state);
                                                    if ($item) {
                                                        $set('item_name', $item->name);
                                                        $set('item_description', $item->description);
                                                        $set('unit', $item->satuan->nama ?? 'Liter');
                                                    }
                                                }
                                            }),

                                        Forms\Components\TextInput::make('item_name')
                                            ->label('Nama Item')
                                            ->disabled()
                                            ->dehydrated()
                                            ->placeholder('Otomatis terisi'),

                                        Forms\Components\TextInput::make('quantity')
                                            ->label('Volume/Quantity')
                                            ->numeric()
                                            ->required()
                                            ->minValue(0.01)
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                static::calculateItemTotals($set, $get);
                                            }),

                                        Forms\Components\TextInput::make('unit')
                                            ->label('Satuan')
                                            ->default('Liter')
                                            ->required(),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Harga Satuan')
                                            ->numeric()
                                            ->prefix(function (callable $get) {
                                                $letterSettingId = $get('../../letter_setting_id');
                                                if ($letterSettingId) {
                                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                                    return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                                }
                                                return 'Rp';
                                            })
                                            ->required(function (callable $get) {
                                                // Not required for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return $transaksi && $transaksi->tipe !== 'jasa';
                                                }
                                                return true;
                                            })
                                            ->visible(function (callable $get) {
                                                // Hide for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return !($transaksi && $transaksi->tipe === 'jasa');
                                                }
                                                return true;
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                static::calculateItemTotals($set, $get);
                                            }),

                                        Forms\Components\TextInput::make('total_amount')
                                            ->label('Total')
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->disabled()
                                            ->dehydrated(),
                                    ]),

                                Forms\Components\Fieldset::make('Pajak & Biaya Tambahan')
                                    ->schema([
                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_ppn')
                                                    ->label('PPN')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('ppn_rate')
                                                    ->label('Tarif PPN (%)')
                                                    ->numeric()
                                                    ->default(11)
                                                    ->suffix('%')
                                                    ->visible(fn(callable $get) => $get('include_ppn'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('ppn_amount')
                                                    ->label('Jumlah PPN')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_ppn')),
                                            ]),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_operasional')
                                                    ->label('Operasional')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->visible(function (callable $get) {
                                                        // Hide for service type SO
                                                        $idTransaksi = $get('../../../id_transaksi');
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            return !($transaksi && $transaksi->tipe === 'jasa');
                                                        }
                                                        return true;
                                                    })
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('operasional_rate')
                                                    ->label('Tarif per Liter')
                                                    ->numeric()
                                                    ->default(968)
                                                    ->prefix('Rp')
                                                    ->visible(fn(callable $get) => $get('include_operasional'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('operasional_amount')
                                                    ->label('Jumlah Operasional')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_operasional')),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Hide entire grid for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return !($transaksi && $transaksi->tipe === 'jasa');
                                                }
                                                return true;
                                            }),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_pbbkb')
                                                    ->label('PBBKB')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->visible(function (callable $get) {
                                                        // Hide for service type SO
                                                        $idTransaksi = $get('../../../id_transaksi');
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            return !($transaksi && $transaksi->tipe === 'jasa');
                                                        }
                                                        return true;
                                                    })
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        // Ensure pbbkb_rate has a default value when toggled
                                                        if ($get('include_pbbkb') && !$get('pbbkb_rate')) {
                                                            $set('pbbkb_rate', 0);
                                                        }
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('pbbkb_rate')
                                                    ->label('Tarif per Liter')
                                                    ->numeric()
                                                    ->default(0)
                                                    ->prefix('Rp')
                                                    ->visible(fn(callable $get) => $get('include_pbbkb'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('pbbkb_amount')
                                                    ->label('Jumlah PBBKB')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_pbbkb')),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Hide entire grid for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return !($transaksi && $transaksi->tipe === 'jasa');
                                                }
                                                return true;
                                            }),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Textarea::make('item_description')
                                            ->label('Deskripsi Item')
                                            ->rows(2)
                                            ->disabled()
                                            ->dehydrated()
                                            ->placeholder('Otomatis terisi'),

                                        Forms\Components\Textarea::make('notes')
                                            ->label('Catatan')
                                            ->rows(2)
                                            ->placeholder('Catatan tambahan untuk item ini'),
                                    ]),
                            ])
                            ->addActionLabel('Tambah Item')
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(
                                fn(array $state): ?string =>
                                isset($state['item_name']) && isset($state['quantity'])
                                    ? $state['item_name'] . ' - ' . $state['quantity'] . ' ' . ($state['unit'] ?? 'Unit')
                                    : 'Item Baru'
                            )
                            ->columnSpanFull()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get) {
                                static::updateInvoiceTotals($set, $get);
                            }),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('load_from_transaction')
                                ->label('Muat dari Transaksi Penjualan')
                                ->icon('heroicon-o-arrow-path')
                                ->color('info')
                                ->action(function (callable $set, callable $get) {
                                    $idTransaksi = $get('id_transaksi');
                                    if (!$idTransaksi) return;

                                    $transaksi = \App\Models\TransaksiPenjualan::with('penjualanDetails.item.satuan')->find($idTransaksi);
                                    if (!$transaksi) return;

                                    $items = [];
                                    $isServiceType = $transaksi->tipe === 'jasa';

                                    foreach ($transaksi->penjualanDetails as $detail) {
                                        if ($detail->item) {
                                            if ($isServiceType) {
                                                // For service type: no unit price, will be filled from transport cost
                                                $items[] = [
                                                    'item_id' => $detail->id_item,
                                                    'item_name' => $detail->item->name,
                                                    'item_description' => $detail->item->description,
                                                    'quantity' => $detail->volume_item ?? $detail->volume_item,
                                                    'unit' => $detail->item->satuan->nama ?? 'Unit',
                                                    'unit_price' => 0, // Will be set from transport cost
                                                    'include_ppn' => false,
                                                    'ppn_rate' => 11,
                                                    'include_operasional' => false,
                                                    'include_pbbkb' => false,
                                                    'notes' => 'Dimuat dari SO Jasa - harga dari biaya ongkos angkut',
                                                ];
                                            } else {
                                                // For trade type: normal pricing
                                                $items[] = [
                                                    'item_id' => $detail->id_item,
                                                    'item_name' => $detail->item->name,
                                                    'item_description' => $detail->item->description,
                                                    'quantity' => $detail->volume_item ?? $detail->volume_item,
                                                    'unit' => $detail->item->satuan->nama ?? 'Liter',
                                                    'unit_price' => $detail->harga_jual,
                                                    'include_ppn' => false,
                                                    'ppn_rate' => 11,
                                                    'include_operasional' => false, // User can enable manually
                                                    'operasional_rate' => 968,
                                                    'include_pbbkb' => false, // User can enable manually
                                                    'pbbkb_rate' => 0,
                                                    'notes' => 'Dimuat dari transaksi penjualan',
                                                ];
                                            }
                                        }
                                    }

                                    $set('invoiceItems', $items);
                                })
                                ->visible(fn(callable $get) => !empty($get('id_transaksi'))),
                        ])
                            ->columnSpanFull(),

                        // Perhitungan Total
                        Forms\Components\Grid::make(2)
                            ->schema([
                                // Base amount from invoice items
                                Forms\Components\TextInput::make('subtotal')
                                    ->label('Subtotal (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $letterSettingId = $get('letter_setting_id');
                                        if ($letterSettingId) {
                                            $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                            return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                        }
                                        return 'Rp';
                                    })
                                    ->disabled()
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->helperText('Otomatis dihitung dari total semua item invoice. Saat edit, gunakan tombol "Hitung Ulang Total" jika perlu recalculate.'),

                                // Transport cost (only for service type SO)
                                Forms\Components\TextInput::make('biaya_ongkos_angkut')
                                    ->label('Biaya Ongkos Angkut')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0)
                                    ->reactive()
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return false;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return $transaksi && $transaksi->tipe === 'jasa';
                                    })
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        static::updateTotals($set, $get);
                                        // Update invoice items for service type
                                        static::updateServiceInvoiceItems($set, $get);
                                    })
                                    ->helperText('Khusus untuk SO tipe Jasa - akan dikalikan ke setiap item'),

                                // Operational costs (calculated from items) - only for non-service SO
                                Forms\Components\TextInput::make('biaya_operasional_kerja')
                                    ->label('Total Biaya Operasional (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return true;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return !($transaksi && $transaksi->tipe === 'jasa');
                                    })
                                    ->helperText('Otomatis dihitung dari total operasional semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_operasional_kerja')
                                    ->default(true)
                                    ->dehydrated(),

                                // PPN from invoice items
                                Forms\Components\Placeholder::make('ppn_info')
                                    ->label('Informasi PPN')
                                    ->content('PPN dihitung per item di bagian Detail Item Invoice. Total PPN akan otomatis terakumulasi di sini.')
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('total_pajak')
                                    ->label('Total PPN (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $letterSettingId = $get('letter_setting_id');
                                        if ($letterSettingId) {
                                            $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                            return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                        }
                                        return 'Rp';
                                    })
                                    ->disabled()
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->helperText('Otomatis dihitung dari total PPN semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_ppn')
                                    ->default(true)
                                    ->dehydrated(),

                                // PBBKB costs (calculated from items) - only for non-service SO
                                Forms\Components\TextInput::make('biaya_pbbkb')
                                    ->label('Total Biaya PBBKB (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return true;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return !($transaksi && $transaksi->tipe === 'jasa');
                                    })
                                    ->helperText('Otomatis dihitung dari total PBBKB semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_pbbkb')
                                    ->default(true)
                                    ->dehydrated(),

                                // Auto-calculated total
                                Forms\Components\TextInput::make('total_invoice')
                                    ->label('TOTAL INVOICE')
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $letterSettingId = $get('letter_setting_id');
                                        if ($letterSettingId) {
                                            $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                            return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                        }
                                        return 'Rp';
                                    })
                                    ->disabled()
                                    ->dehydrated() // Important: This ensures the value is saved even when disabled
                                    ->extraAttributes(['class' => 'font-bold text-lg'])
                                    ->helperText('Total otomatis dihitung dari semua komponen')
                                    ->columnSpanFull(),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Status & Pembayaran')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'draft' => 'Draft',
                                'sent' => 'Terkirim',
                                'paid' => 'Lunas',
                                'overdue' => 'Jatuh Tempo',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->default('draft')
                            ->required(),

                        Forms\Components\TextInput::make('total_terbayar')
                            ->label('Total Terbayar')
                            ->numeric()
                            ->prefix(function (callable $get) {
                                $letterSettingId = $get('letter_setting_id');
                                if ($letterSettingId) {
                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                    return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                }
                                return 'Rp';
                            })
                            ->default(0),

                        Forms\Components\TextInput::make('sisa_tagihan')
                            ->label('Sisa Tagihan')
                            ->numeric()
                            ->prefix(function (callable $get) {
                                $letterSettingId = $get('letter_setting_id');
                                if ($letterSettingId) {
                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                    return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                }
                                return 'Rp';
                            })
                            ->default(0),

                        Forms\Components\Textarea::make('catatan')
                            ->label('Catatan')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),






            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomor_invoice')
                    ->label('Nomor Invoice')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaksiPenjualan.nomor_transaksi')
                    ->label('Nomor Transaksi')
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('transaksiPenjualan', function ($q) use ($search) {
                            $q->where('id', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('delivery_orders_count')
                    ->label('Jumlah DO')
                    ->getStateUsing(function ($record) {
                        try {
                            if (!$record->transaksiPenjualan) {
                                return 0;
                            }

                            // Use query instead of relationship property to avoid eager loading issues
                            return \App\Models\DeliveryOrder::where('id_transaksi', $record->id_transaksi)->count();
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('Error getting delivery orders count: ' . $e->getMessage());
                            return 0;
                        }
                    })
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('nama_pelanggan')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_invoice')
                    ->label('Tanggal Invoice')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_jatuh_tempo')
                    ->label('Jatuh Tempo')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_invoice')
                    ->label('Total Invoice')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->formatCurrency($state);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('sisa_tagihan')
                    ->label('Sisa Tagihan')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->formatCurrency($state);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'sent' => 'warning',
                        'paid' => 'success',
                        'overdue' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'sent' => 'Terkirim',
                        'paid' => 'Lunas',
                        'overdue' => 'Jatuh Tempo',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'sent' => 'Terkirim',
                        'paid' => 'Lunas',
                        'overdue' => 'Jatuh Tempo',
                        'cancelled' => 'Dibatalkan',
                    ]),

                Tables\Filters\SelectFilter::make('transaksi_penjualan')
                    ->label('Transaksi Penjualan')
                    ->relationship('transaksiPenjualan', 'id')
                    ->getOptionLabelFromRecordUsing(fn($record) => $record->nomor_transaksi . ' - ' . $record->pelanggan->nama)
                    ->searchable(['id'])
                    // populate dari id_transaksi
                    ->default(function () {
                        return request()->query('id_transaksi', null);
                    })
                    ->preload(),

                Tables\Filters\Filter::make('tanggal_invoice')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_invoice', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_invoice', '<=', $date),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('preview')
                    ->label('Preview')
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->action(null)
                    ->modalContent(function (Invoice $record): \Illuminate\View\View {
                        // Load the record with all necessary relationships including letterSetting
                        $record->load([
                            'transaksiPenjualan.pelanggan.alamatUtama',
                            'transaksiPenjualan.penjualanDetails.item.satuan',
                            'transaksiPenjualan.sph.letterSetting',
                            'createdBy.jabatan',
                            'invoiceItems.item',
                            'letterSetting'
                        ]);

                        // Get ISO certifications
                        $isoCertifications = \App\Models\IsoCertification::where('is_active', true)->get();

                        // Determine the locale from letter setting
                        $locale = $record->getLocale();
                        $viewName = "invoice.invoice-preview-{$locale}";

                        // Check if the view exists, fallback to Indonesian if not
                        if (!\Illuminate\Support\Facades\View::exists($viewName)) {
                            $viewName = 'invoice.invoice-preview';
                        }

                        return \Illuminate\Support\Facades\View::make($viewName, [
                            'record' => $record,
                            'isoCertifications' => $isoCertifications
                        ]);
                    })
                    ->modalHeading(fn(Invoice $record) => "Preview: {$record->nomor_invoice}")
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Tutup')
                    ->slideOver()
                    ->modalWidth('4xl'),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('print_tax_invoice')
                    ->label('Cetak Faktur Pajak')
                    ->icon('heroicon-o-document-text')
                    ->color('warning')
                    ->visible(fn(Invoice $record): bool => Auth::user()?->can('view', $record) ?? false)
                    ->action(function (Invoice $record) {
                        try {
                            // Load the invoice with all necessary relationships
                            $invoice = Invoice::with([
                                'transaksiPenjualan.pelanggan.alamatUtama',
                                'transaksiPenjualan.pelanggan.subdistrict.district.regency',
                                'transaksiPenjualan.penjualanDetails.item.satuanDasar',
                                'transaksiPenjualan.sph.letterSetting',
                                'invoiceItems.item',
                                'createdBy',
                                'letterSetting'
                            ])->find($record->id);

                            // Generate dynamic filename
                            $filename = 'FakturPajak_' . str_replace(['/', '\\', ' '], '_', $invoice->nomor_invoice) . '_' . now()->format('Ymd_His') . '.pdf';

                            // Get logo as base64
                            $logoPath = public_path('images/lrp.png');
                            $logoBase64 = '';

                            if (File::exists($logoPath)) {
                                $logoBase64 = base64_encode(File::get($logoPath));
                            }

                            // Determine the locale from letter setting
                            $locale = $invoice->getLocale();
                            $viewName = "pdf.tax_invoice_from_invoice_{$locale}";

                            // Check if the view exists, fallback to Indonesian if not
                            if (!\Illuminate\Support\Facades\View::exists($viewName)) {
                                $viewName = 'pdf.tax_invoice_from_invoice';
                            }

                            // Load the PDF view with the record data
                            $pdf = Pdf::loadView($viewName, [
                                'record' => $invoice,
                                'logoBase64' => $logoBase64
                            ])
                                ->setPaper('a4', 'portrait')
                                ->setOptions([
                                    'isHtml5ParserEnabled' => true,
                                    'isPhpEnabled' => true,
                                    'defaultFont' => 'Arial',
                                    'dpi' => 150,
                                    'defaultPaperSize' => 'a4',
                                    'chroot' => public_path(),
                                ]);

                            return response()->streamDownload(function () use ($pdf) {
                                echo $pdf->output();
                            }, $filename, [
                                'Content-Type' => 'application/pdf',
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error generating tax invoice PDF: ' . $e->getMessage());

                            Notification::make()
                                ->title('Error')
                                ->body('Gagal membuat PDF Faktur Pajak: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('print_pdf')
                    ->label('Cetak PDF')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->visible(fn(Invoice $record): bool => Auth::user()?->can('view', $record) ?? false)
                    ->action(function (Invoice $record) {
                        try {
                            // Load the invoice with all necessary relationships including letterSetting
                            $invoice = Invoice::with([
                                'transaksiPenjualan.pelanggan.alamatUtama',
                                'transaksiPenjualan.pelanggan.subdistrict.district.regency',
                                'transaksiPenjualan.penjualanDetails.item.satuanDasar',
                                'transaksiPenjualan.sph.letterSetting',
                                'invoiceItems.item',
                                'createdBy',
                                'letterSetting'
                            ])->find($record->id);

                            // Generate dynamic filename
                            $filename = 'Invoice_' . $invoice->nomor_invoice . '_' . now()->format('Ymd_His') . '.pdf';

                            // Get logo as base64
                            $logoPath = public_path('images/lrp.png');
                            $logoBase64 = '';

                            if (File::exists($logoPath)) {
                                $logoBase64 = base64_encode(File::get($logoPath));
                            }

                            // Determine the locale from letter setting
                            $locale = $invoice->getLocale();
                            $viewName = "pdf.invoice_{$locale}";

                            // Check if the view exists, fallback to Indonesian if not
                            if (!\Illuminate\Support\Facades\View::exists($viewName)) {
                                $viewName = 'pdf.invoice';
                            }

                            // Load the PDF view with the record data
                            $pdf = Pdf::loadView($viewName, [
                                'record' => $invoice,
                                'logoBase64' => $logoBase64
                            ])
                                ->setPaper('a4', 'portrait')
                                ->setOptions([
                                    'isHtml5ParserEnabled' => true,
                                    'isPhpEnabled' => true,
                                    'defaultFont' => 'Arial'
                                ]);

                            // Stream the PDF as a download
                            return response()->streamDownload(function () use ($pdf) {
                                echo $pdf->output();
                            }, $filename);
                        } catch (\Exception $e) {
                            // Log the error for debugging
                            Log::error('Failed to generate Invoice PDF: ' . $e->getMessage());
                            Log::error('Invoice PDF Error Stack Trace: ' . $e->getTraceAsString());

                            // Show notification to user
                            \Filament\Notifications\Notification::make()
                                ->title('Error generating PDF')
                                ->body('Failed to generate PDF. Please try again or contact administrator.')
                                ->danger()
                                ->send();

                            return;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'transaksiPenjualan.pelanggan',
                // 'transaksiPenjualan.deliveryOrder',
                'createdBy'
            ])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * Calculate item totals (subtotal, PPN, operasional, PBBKB, total amount)
     */
    protected static function calculateItemTotals(callable $set, callable $get): void
    {

        $quantity = (float) $get('quantity') ?: 0;
        $unitPrice = (float) $get('unit_price') ?: 0;

        // Check if this is a service type transaction
        $idTransaksi = $get('../../id_transaksi');
        $isServiceType = false;
        $biayaOngkosAngkut = 0;

        if ($idTransaksi) {
            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
            $isServiceType = $transaksi && $transaksi->tipe === 'jasa';

            if ($isServiceType) {
                // For service type, get transport cost from parent form
                $biayaOngkosAngkut = (float) $get('../../biaya_ongkos_angkut') ?: 0;
                $unitPrice = $biayaOngkosAngkut; // Use transport cost as unit price
                $set('unit_price', $unitPrice);
            }
        }

        // Calculate subtotal
        $subtotal = $quantity * $unitPrice;
        $set('subtotal', $subtotal);

        // For service type, only calculate PPN, no operational or PBBKB
        if ($isServiceType) {
            // Calculate PPN amount
            $includePpn = $get('include_ppn') ?: false;
            $ppnRate = (float) $get('ppn_rate') ?: 11;
            $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
            $set('ppn_amount', $ppnAmount);

            // Set operational and PBBKB to 0 for service type
            $set('include_operasional', false);
            $set('operasional_amount', 0);
            $set('include_pbbkb', false);
            $set('pbbkb_amount', 0);

            // Calculate total amount (only subtotal + PPN for service)
            $totalAmount = $subtotal + $ppnAmount;
            $set('total_amount', $totalAmount);
        } else {
            // Normal calculation for non-service type
            // Calculate PPN amount
            $includePpn = $get('include_ppn') ?: false;
            $ppnRate = (float) $get('ppn_rate') ?: 11;
            $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
            $set('ppn_amount', $ppnAmount);

            // Calculate operational amount (per liter)
            $includeOperasional = $get('include_operasional') ?: false;
            $operasionalRate = (float) $get('operasional_rate') ?: 968;
            $operasionalAmount = $includeOperasional ? ($quantity * $operasionalRate) : 0;
            $set('operasional_amount', $operasionalAmount);

            // Calculate PBBKB amount (per liter)
            $includePbbkb = $get('include_pbbkb') ?: false;
            $pbbkbRate = (float) $get('pbbkb_rate') ?: 0;
            $pbbkbAmount = $includePbbkb ? ($quantity * $pbbkbRate) : 0;
            $set('pbbkb_amount', $pbbkbAmount);

            // Calculate total amount
            $totalAmount = $subtotal + $ppnAmount + $operasionalAmount + $pbbkbAmount;
            $set('total_amount', $totalAmount);
        }
    }

    /**
     * Update service invoice items when transport cost changes
     */
    protected static function updateServiceInvoiceItems(callable $set, callable $get): void
    {
        $biayaOngkosAngkut = (float) $get('biaya_ongkos_angkut') ?: 0;
        $invoiceItems = $get('invoiceItems') ?: [];

        // Update all invoice items with new transport cost as unit price
        foreach ($invoiceItems as $index => $item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $subtotal = $quantity * $biayaOngkosAngkut;

            // Update the item
            $set("invoiceItems.{$index}.unit_price", $biayaOngkosAngkut);
            $set("invoiceItems.{$index}.subtotal", $subtotal);

            // Recalculate PPN if included
            $includePpn = $item['include_ppn'] ?? false;
            $ppnRate = (float) ($item['ppn_rate'] ?? 11);
            $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
            $set("invoiceItems.{$index}.ppn_amount", $ppnAmount);

            // Set total amount (subtotal + PPN only for service)
            $totalAmount = $subtotal + $ppnAmount;
            $set("invoiceItems.{$index}.total_amount", $totalAmount);
        }

        // Update invoice totals
        static::updateInvoiceTotals($set, $get);
    }

    /**
     * Update invoice totals based on invoice items
     */
    protected static function updateInvoiceTotals(callable $set, callable $get): void
    {

        $invoiceItems = $get('invoiceItems') ?: [];

        $totalSubtotal = 0;
        $totalPpn = 0;
        $totalOperasional = 0;
        $totalPbbkb = 0;
        $totalVolume = 0;

        foreach ($invoiceItems as $item) {
            $totalSubtotal += (float) ($item['subtotal'] ?? 0);
            $totalPpn += (float) ($item['ppn_amount'] ?? 0);
            $totalOperasional += (float) ($item['operasional_amount'] ?? 0);
            $totalPbbkb += (float) ($item['pbbkb_amount'] ?? 0);
            $totalVolume += (float) ($item['quantity'] ?? 0);
        }

        $set('subtotal', $totalSubtotal);
        $set('total_pajak', $totalPpn);
        $set('biaya_operasional_kerja', $totalOperasional);
        $set('biaya_pbbkb', $totalPbbkb);

        // Update volume for reference
        $set('operasional_volume', $totalVolume);
        $set('pbbkb_volume', $totalVolume);

        // Recalculate total invoice
        static::updateTotals($set, $get);
    }

    /**
     * Calculate operational costs based on rate and volume
     */
    protected static function calculateOperasional(callable $set, callable $get): void
    {
        $rate = (float) $get('operasional_rate') ?: 0;
        $volume = (float) $get('operasional_volume') ?: 0;
        $total = $rate * $volume;

        $set('biaya_operasional_kerja', $total);
    }

    /**
     * Calculate PBBKB based on rate per 10,000L and volume
     */
    protected static function calculatePbbkb(callable $set, callable $get): void
    {
        $rate = (float) $get('pbbkb_rate') ?: 0;
        $volume = (float) $get('pbbkb_volume') ?: 0;

        // PBBKB calculation: (rate per 10,000L) * (volume / 10,000)
        $total = $rate * ($volume / 10000);

        $set('biaya_pbbkb', $total);
    }

    /**
     * Update all totals based on current values
     */
    protected static function updateTotals(callable $set, callable $get): void
    {
        try {
            // Check if this is a service type transaction
            $idTransaksi = $get('id_transaksi');
            $isServiceType = false;

            if ($idTransaksi) {
                $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                $isServiceType = $transaksi && $transaksi->tipe === 'jasa';
            }

            // Get base values with proper fallbacks
            $subtotal = (float) ($get('subtotal') ?? 0);
            $totalPajak = (float) ($get('total_pajak') ?? 0);

            if ($isServiceType) {
                // For service type: subtotal + transport cost + PPN
                $biayaOngkos = (float) ($get('biaya_ongkos_angkut') ?? 0);
                $totalInvoice = $subtotal + $biayaOngkos + $totalPajak;

                \Illuminate\Support\Facades\Log::info('Service type calculation', [
                    'subtotal' => $subtotal,
                    'biaya_ongkos' => $biayaOngkos,
                    'total_pajak' => $totalPajak,
                    'total_invoice' => $totalInvoice
                ]);
            } else {
                // For trade type: include all costs
                $biayaOngkos = (float) ($get('biaya_ongkos_angkut') ?? 0);

                // Calculate operational if included
                $biayaOperasional = 0;
                if ($get('include_operasional_kerja')) {
                    $biayaOperasional = (float) ($get('biaya_operasional_kerja') ?? 0);
                }

                // Calculate PBBKB if included
                $biayaPbbkb = 0;
                if ($get('include_pbbkb')) {
                    $biayaPbbkb = (float) ($get('biaya_pbbkb') ?? 0);
                }

                // Calculate final total
                $totalInvoice = $subtotal + $biayaOngkos + $biayaOperasional + $totalPajak + $biayaPbbkb;

                \Illuminate\Support\Facades\Log::info('Trade type calculation', [
                    'subtotal' => $subtotal,
                    'biaya_ongkos' => $biayaOngkos,
                    'biaya_operasional' => $biayaOperasional,
                    'total_pajak' => $totalPajak,
                    'biaya_pbbkb' => $biayaPbbkb,
                    'total_invoice' => $totalInvoice
                ]);
            }

            $set('total_invoice', $totalInvoice);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in updateTotals: ' . $e->getMessage());
        }
    }

    /**
     * Populate form data from selected transaksi penjualan
     */
    protected static function populateFromTransaksiPenjualan(callable $set, callable $get, $idTransaksi): void
    {
        if (!$idTransaksi) return;

        try {
            // Load transaksi penjualan with all necessary relationships
            $transaksi = \App\Models\TransaksiPenjualan::with([
                'pelanggan',
                'alamatPelanggan',
                'penjualanDetails.item',
                'sph.letterSetting'
            ])->find($idTransaksi);

            if (!$transaksi) {
                \Illuminate\Support\Facades\Log::warning('Transaksi not found: ' . $idTransaksi);
                return;
            }

            $pelanggan = $transaksi->pelanggan;
            $alamatPelanggan = $transaksi->alamatPelanggan;

            \Illuminate\Support\Facades\Log::info('Populating invoice from transaksi', [
                'transaksi_id' => $idTransaksi,
                'pelanggan_nama' => $pelanggan?->nama,
                'alamat_pelanggan' => $alamatPelanggan?->alamat
            ]);

            // Auto-populate customer data
            if ($pelanggan) {
                \Illuminate\Support\Facades\Log::info('Setting customer data', [
                    'nama' => $pelanggan->nama,
                    'npwp' => $pelanggan->npwp
                ]);
                $set('nama_pelanggan', $pelanggan->nama);
                $set('npwp_pelanggan', $pelanggan->npwp);
            } else {
                \Illuminate\Support\Facades\Log::warning('No pelanggan found for transaksi: ' . $idTransaksi);
            }

            // Auto-populate customer address
            if ($alamatPelanggan) {
                $alamatLengkap = collect([
                    $alamatPelanggan->alamat,
                    $alamatPelanggan->kelurahan,
                    $alamatPelanggan->kecamatan,
                    $alamatPelanggan->kota,
                    $alamatPelanggan->provinsi,
                    $alamatPelanggan->kode_pos
                ])->filter()->implode(', ');

                \Illuminate\Support\Facades\Log::info('Setting alamat pelanggan', ['alamat' => $alamatLengkap]);
                $set('alamat_pelanggan', $alamatLengkap);
            } else {
                // Fallback to pelanggan alamat if no specific address
                $fallbackAlamat = $pelanggan->alamat ?? '';
                \Illuminate\Support\Facades\Log::info('Setting fallback alamat', ['alamat' => $fallbackAlamat]);
                $set('alamat_pelanggan', $fallbackAlamat);
            }

            // Auto-fill letter setting from SPH or default
            if ($transaksi->sph && $transaksi->sph->letterSetting) {
                // If transaksi has SPH and SPH has letter setting, use it
                $set('letter_setting_id', $transaksi->sph->letter_setting_id);
                \Illuminate\Support\Facades\Log::info('Setting letter_setting_id from SPH', [
                    'letter_setting_id' => $transaksi->sph->letter_setting_id,
                    'setting_name' => $transaksi->sph->letterSetting->name
                ]);
            } else {
                // Otherwise, use default letter setting
                $defaultLetterSetting = \App\Models\LetterSetting::where('is_default', true)->first();
                if ($defaultLetterSetting) {
                    $set('letter_setting_id', $defaultLetterSetting->id);
                    \Illuminate\Support\Facades\Log::info('Setting default letter_setting_id', [
                        'letter_setting_id' => $defaultLetterSetting->id,
                        'setting_name' => $defaultLetterSetting->name
                    ]);
                }
            }

            // Calculate subtotal from penjualan details
            $subtotal = 0;
            $totalVolume = 0;

            foreach ($transaksi->penjualanDetails as $detail) {
                $itemTotal = ($detail->volume_item ?? $detail->volume_item) * $detail->harga_jual;
                $subtotal += $itemTotal;
                $totalVolume += ($detail->volume_item ?? $detail->volume_item);
            }

            // Set calculated values
            $set('subtotal', $subtotal);

            // Set default volumes for calculations
            $set('operasional_volume', $totalVolume);
            $set('pbbkb_volume', $totalVolume);

            // Set default dates
            $set('tanggal_invoice', now());
            $set('tanggal_jatuh_tempo', now()->addDays(30));

            // Set default status
            $set('status', 'draft');

            // Trigger total calculation
            static::updateTotals($set, $get);
        } catch (\Exception $e) {
            // Log error but don't break the form
            \Illuminate\Support\Facades\Log::error('Error populating invoice from transaksi: ' . $e->getMessage());
        }
    }
}
