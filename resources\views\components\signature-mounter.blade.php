{{--
    This component displays a user's signature overlaid on a company stamp.
    It expects a 'user' object to be passed to it.
--}}
@props(['user'])

{{-- The main container that holds all elements --}}
<div style="width: 250px; height: auto; margin-top: 1rem; text-align: center;">

    {{-- Flex container for the images to sit side-by-side --}}
    <div style="display: flex; align-items: center; justify-content: center; height: 70px;">
        
        <!-- if there r user signature, load the signature -->
        @if($user && $user->signature_path)
            {{-- The company stamp/logo is the first item on the left --}}
            <img src="{{ asset('storage/business-logos/lrp-logo-noBG.png') }}" alt="Company Stamp" 
                style="
                    width: 120px; 
                    height: auto; 
                    display: block; 
                    /* margin-left: -30px;  */
                    z-index: 1;
                    transform: translateX(40%);
                "
            >
            
            {{-- The user's signature is the second item, slightly overlapping --}}
            <img src="{{ $user->signature_url }}" alt="Signature" 
                style="
                    width: 250px;
                    height: auto;
                    mix-blend-mode: darken;
                    z-index: 10;
                    transform: translateX(-15%);
                "
            >
        @endif
    </div>

</div>
