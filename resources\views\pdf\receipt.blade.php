<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kwitansi {{ $record->nomor_receipt ?? $record->nomor_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            padding: 20px;
        }

        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            border: 2px solid #333;
            padding: 30px;
            background: white;
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            position: relative;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .company-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .company-logo-text {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            padding: 8px;
            border-radius: 8px;
            line-height: 1.2;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        .company-details {
            flex: 1;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .company-tagline {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 2px;
        }

        .receipt-title {
            text-align: center;
            flex: 1;
        }

        .receipt-title h1 {
            font-size: 32px;
            font-weight: bold;
            color: #1f2937;
            text-decoration: underline;
            margin: 0;
            letter-spacing: 2px;
        }

        .receipt-number {
            text-align: right;
            font-size: 12px;
            color: #374151;
            white-space: nowrap;
            min-width: 180px;
        }

        .receipt-body {
            margin-bottom: 40px;
        }

        .receipt-row {
            display: flex;
            margin-bottom: 25px;
            align-items: flex-start;
        }

        .receipt-label {
            width: 180px;
            font-weight: normal;
            color: #374151;
            flex-shrink: 0;
        }

        .receipt-colon {
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .receipt-value {
            flex: 1;
            border-bottom: 1px solid #333;
            min-height: 25px;
            padding-bottom: 5px;
            color: #1f2937;
            font-weight: 500;
        }

        .amount-row .receipt-value {
            font-weight: bold;
            font-size: 16px;
        }

        .terbilang-row .receipt-value {
            font-style: italic;
            color: #374151;
        }

        .payment-description {
            min-height: 60px;
            padding: 10px 0;
        }

        .signature-section {
            margin-top: 60px;
            display: flex;
            justify-content: flex-end;
        }

        .signature-box {
            text-align: center;
            width: 250px;
        }

        .signature-location {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .signature-space {
            height: 80px;
            margin: 20px 0;
            position: relative;
        }

        .signature-name {
            font-weight: bold;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
            margin-bottom: 5px;
        }

        .signature-title {
            font-size: 12px;
            color: #374151;
        }

        @media print {
            body {
                margin: 0;
                padding: 10px;
            }

            .receipt-container {
                border: 2px solid #333;
                box-shadow: none;
            }
        }
    </style>
</head>

<body>
    <div class="receipt-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-logo">
                @if (isset($logoBase64) && $logoBase64)
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="LRP Logo">
                @else
                    <div class="company-logo-text">
                        LINTAS<br>RIAU<br>PRIMA
                    </div>
                @endif
            </div>

            <div class="receipt-title">
                <h1>KWITANSI</h1>
            </div>

            <div class="receipt-number">
                @if (isset($record->nomor_receipt))
                    No. Receipt : {{ $record->nomor_receipt }}
                @else
                    No. Invoice : {{ $record->nomor_invoice }}
                @endif
            </div>
        </div>

        <!-- Receipt Body -->
        <div class="receipt-body">
            <div class="receipt-row">
                <div class="receipt-label">Sudah Terima Dari</div>
                <div class="receipt-colon">:</div>
                <div class="receipt-value">
                    @if (isset($record->nomor_receipt))
                        {{-- Receipt object --}}
                        {{ $record->transaksiPenjualan->pelanggan->nama ?? ($record->invoice->transaksiPenjualan->pelanggan->nama ?? '-') }}
                    @else
                        {{-- Invoice object --}}
                        {{ $record->transaksiPenjualan->pelanggan->nama ?? '-' }}
                    @endif
                </div>
            </div>

            <div class="receipt-row amount-row">
                <div class="receipt-label">Uang Sebanyak</div>
                <div class="receipt-colon">:</div>
                <div class="receipt-value">Rp
                    @if (isset($record->nomor_receipt))
                        {{-- Receipt object --}}
                        {{ number_format($record->jumlah_pembayaran ?: $record->invoice->total_invoice ?? 0, 0, ',', '.') }}
                    @else
                        {{-- Invoice object --}}
                        {{ number_format($record->total_invoice ?? 0, 0, ',', '.') }}
                    @endif
                </div>
            </div>

            <div class="receipt-row terbilang-row">
                <div class="receipt-label">Terbilang</div>
                <div class="receipt-colon">:</div>
                <div class="receipt-value">
                    @if (isset($record->nomor_receipt))
                        {{-- Receipt object --}}
                        "{{ ucfirst(\App\Helpers\NumberToWords::convert($record->jumlah_pembayaran ?: $record->invoice->total_invoice ?? 0)) }}
                        rupiah"
                    @else
                        {{-- Invoice object --}}
                        "{{ ucfirst(\App\Helpers\NumberToWords::convert($record->total_invoice ?? 0)) }} rupiah"
                    @endif
                </div>
            </div>

            <div class="receipt-row">
                <div class="receipt-label">Untuk Pembayaran</div>
                <div class="receipt-colon">:</div>
                <div class="receipt-value payment-description">
                    @if (isset($record->nomor_receipt))
                        {{-- Receipt object --}}
                        @if ($record->deliveryOrder)
                            {{ number_format($record->deliveryOrder->volume_bbm, 0, ',', '.') }} liter BBM Biosolar
                            Industri ke {{ $record->deliveryOrder->alamat_tujuan }}.
                        @else
                            Pembayaran Invoice {{ $record->invoice->nomor_invoice ?? $record->nomor_receipt }} untuk
                            layanan pengiriman BBM.
                        @endif
                    @else
                        {{-- Invoice object --}}
                        Pembayaran Invoice {{ $record->nomor_invoice }} untuk layanan pengiriman BBM.
                    @endif
                </div>
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-location">Pekanbaru,
                    @if (isset($record->nomor_receipt))
                        {{ $record->tanggal_pembayaran ? $record->tanggal_pembayaran->format('d-M-y') : now()->format('d-M-y') }}
                    @else
                        {{ now()->format('d-M-y') }}
                    @endif
                </div>
                <div class="signature-space">
                    @if (isset($logoBase64) && $logoBase64)
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="LRP Logo"
                            style="width: 60px; height: 60px; margin: 10px auto; display: block;">
                    @else
                        <div class="company-logo-text"
                            style="width: 60px; height: 60px; margin: 10px auto; font-size: 10px; padding: 5px;">
                            LINTAS<br>RIAU<br>PRIMA
                        </div>
                    @endif
                </div>
                <div class="signature-name">Agustiawan Syahputra</div>
                <div class="signature-title">Direktur</div>
            </div>
        </div>
    </div>
</body>

</html>
