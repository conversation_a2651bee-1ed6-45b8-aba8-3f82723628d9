<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JabatanResource\Pages;
use App\Filament\Resources\JabatanResource\RelationManagers;
use App\Models\Jabatan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class JabatanResource extends Resource
{
    protected static ?string $model = Jabatan::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?string $navigationLabel = 'Jabatan';

    protected static ?string $modelLabel = 'Jabatan';

    protected static ?string $pluralModelLabel = 'Jabatan';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Jabatan')
                    ->description('Data jabatan/posisi dalam organisasi')
                    ->icon('heroicon-o-briefcase')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('nama')
                                    ->label('Nama Jabatan (Indonesia)')
                                    ->placeholder('Contoh: Manajer Operasional')
                                    ->required()
                                    ->maxLength(100)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                        if ($operation !== 'create') {
                                            return;
                                        }
                                        // Auto-suggest English translation based on common positions
                                        $suggestions = [
                                            'direktur' => 'Director',
                                            'manajer' => 'Manager',
                                            'supervisor' => 'Supervisor',
                                            'staff' => 'Staff',
                                            'admin' => 'Admin',
                                            'sekretaris' => 'Secretary',
                                            'akuntan' => 'Accountant',
                                            'driver' => 'Driver',
                                            'operator' => 'Operator',
                                            'teknisi' => 'Technician',
                                        ];

                                        $lowerState = strtolower($state);
                                        foreach ($suggestions as $id => $en) {
                                            if (str_contains($lowerState, $id)) {
                                                $set('jabatan_en', $en);
                                                break;
                                            }
                                        }
                                    }),

                                Forms\Components\TextInput::make('jabatan_en')
                                    ->label('Nama Jabatan (English)')
                                    ->placeholder('Contoh: Operations Manager')
                                    ->maxLength(100)
                                    ->helperText('Opsional: Nama jabatan dalam bahasa Inggris'),
                            ]),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Informasi Sistem')
                    ->description('Data sistem dan audit')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Dibuat pada')
                            ->content(fn(Jabatan $record): ?string => $record->created_at?->format('d/m/Y H:i:s'))
                            ->visible(fn($operation) => $operation === 'edit'),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Diperbarui pada')
                            ->content(fn(Jabatan $record): ?string => $record->updated_at?->format('d/m/Y H:i:s'))
                            ->visible(fn($operation) => $operation === 'edit'),

                        Forms\Components\Placeholder::make('created_by')
                            ->label('Dibuat oleh')
                            ->content(fn(Jabatan $record): ?string => $record->createdBy?->name)
                            ->visible(fn($operation) => $operation === 'edit'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJabatans::route('/'),
            'create' => Pages\CreateJabatan::route('/create'),
            'edit' => Pages\EditJabatan::route('/{record}/edit'),
        ];
    }
}
