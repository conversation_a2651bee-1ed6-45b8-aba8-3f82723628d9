<!-- include -->
@php 
    use App\Support\Formatter;
@endphp

<!-- logic -->
@php
    // The $record variable is passed in.
    $letterSetting = $record->letterSetting;

    // Fetch ISO certifications (this logic remains)
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();

    // SIGNATURE SET PART
    $signer = null;
    
    // First, check if there is a final, approved record.
    $finalApproval = $record->approvedApprovals; // This uses the HasOne relationship

    if ($finalApproval) {
        // If an approved record exists, the signer is the user who approved it.
        $signer = $finalApproval->user;
    } else {
        // If not yet approved, find the default manager from the notification settings.
        $eventName = 'sph_manager_update_sales'; // The event name for the SPH manager
        $defaultApproverSetting = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName)->first();
        
        if ($defaultApproverSetting) {
            $signer = $defaultApproverSetting->user;
        } 
    }
@endphp

<div class="p-4 sm:p-6 bg-white font-sans text-gray-800">
    
    <div class="flex justify-end mb-4 print:hidden">
        {{-- Download button remains the same --}}
        <a href="#" {{-- href="{{ route('sph.pdf.download', $record) }}" --}}
           target="_blank"
           class="inline-flex items-center justify-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-500 focus:outline-none focus:border-primary-700 focus:ring focus:ring-primary-200 active:bg-primary-700 disabled:opacity-25 transition">
            <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v6.19l1.97-1.97a.75.75 0 111.06 1.06l-3.25 3.25a.75.75 0 01-1.06 0l-3.25-3.25a.75.75 0 111.06-1.06l1.97 1.97V3.75A.75.75 0 0110 3zM3.75 14.25a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H4.5a.75.75 0 01-.75-.75z" clip-rule="evenodd" /></svg>
            Download PDF
        </a>
    </div>

    {{-- Header Section --}}
    <header class="flex justify-between items-start mb-8">
        <div>
            <img src="{{ asset('storage/business-logos/lrp-colored.png') }}" alt="Company Logo" style="height: 50px;" class="mb-2">
        </div>
        <div class="text-right">
            <h2 class="font-bold text-lg">TRUSTED & RELIABLE PARTNER</h2>
            <p class="text-xs">Fuel Agent – Fuel Transportation – Bunker Service</p>
        </div>
    </header>

    {{-- Document Info Section --}}
    <section class="mb-8">
        <div class="float-right text-sm">
            {{-- DYNAMIC: City from letter setting --}}
            {{ $letterSetting?->city ?? 'Pekanbaru' }}, {{ $record->sph_date->format('F j, Y') }}
        </div>
        <div class="clear-both"></div>

        <table class="text-sm mt-4">
            <tbody>
                <tr>
                    <td class="pr-2">No.</td>
                    <td class="pr-2">:</td>
                    <td class="font-semibold">{{ $record->sph_number }}</td>
                </tr>
                <tr>
                    <td class="pr-2">Attachment</td>
                    <td class="pr-2">:</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td class="pr-2 align-top">Subject</td>
                    <td class="pr-2 align-top">:</td>
                    <td class="font-semibold">Price Quotation for Industrial Pertalite<br>Pertamina Patra Niaga</td>
                </tr>
            </tbody>
        </table>
    </section>

    {{-- Recipient Section --}}
    <section class="mb-8 text-sm">
        <p>To:</p>
        <p class="font-bold">{{ $record->customer?->nama }}</p>
        @if($record->opsional_pic)
            <p>Attn: {{ $record->opsional_pic }}</p>
        @elseif($record->customer?->pic_nama)
             <p>Attn: {{ $record->customer->pic_nama }}</p>
        @endif
        <p>At your location</p>
    </section>

    {{-- Body / Salutation --}}
    <section class="mb-6 text-sm leading-relaxed">
        <p class="mb-4">Dear Sir/Madam,</p>
        <p>
            In connection with the information on the need for industrial fuel, we hereby send a price quotation for the period
            <span class="font-bold">{{ $record->sph_date->format('F j, Y') }}</span> to <span class="font-bold">{{ $record->valid_until_date->format('F j, Y') }}</span>.
        </p>
    </section>

    {{-- Product Offering Section --}}
    <section class="mb-8 text-sm">
        <p class="mb-2 font-bold">The fuel products we offer are:</p>
        <div class="pl-4">
            <table class="w-full">
                <tbody>
                    <tr>
                        <td class="w-4 pr-2 align-top">1.</td>
                        <td class="font-semibold pr-2 w-28">Product Name</td>
                        <td>: Industrial Pertalite.</td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">2.</td>
                        <td class="font-semibold pr-2">Specification</td>
                        <td>: Migas & International ASTM Standard</td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">3.</td>
                        <td class="font-semibold pr-2">Legality</td>
                        <td>: Full Document / Official from PT. Pertamina Patra Niaga</td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">4.</td>
                        <td class="font-semibold pr-2">Source</td>
                        <td>: Pertamina Patra Niaga</td>
                    </tr>
                     <tr>
                        <td class="pr-2 align-top">5.</td>
                        <td class="font-semibold pr-2">TKDN</td>
                        <td>: 99.93% based on the report of the Ministry of Industry RI</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    {{-- Price Details Table --}}
    <section class="mb-8">
        <h2 class="text-lg font-semibold mb-2">The price quotation we provide is as follows:</h2>
        <table class="w-full text-left text-sm border-collapse border border-gray-400">
            <thead>
                <tr class="bg-gray-100">
                    <th class="p-2 border border-gray-300 text-center">No.</th>
                    <th class="p-2 border border-gray-300">Details</th>
                    <th class="p-2 border border-gray-300 text-right">Price/Liter</th>
                </tr>
            </thead>
            <tbody>
                @foreach($record->details as $idx => $detail)
                    <!-- harga dasar -->
                    <tr class="border-t border-gray-300">
                        <td class="p-2 border border-gray-300 text-center">{{ $idx + 1 }}</td>
                        <td class="p-2 border border-gray-300">Base Fuel Price</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->harga_dasar, 'en') }}</td>
                    </tr>

                    <!-- ppn/vat -->
                    <tr>
                        <td class="p-2 border border-gray-300 text-center"></td>
                        <td class="p-2 border border-gray-300">VAT 11%</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->ppn, 'en') }}</td>
                    </tr>

                    <!-- oat -->
                    <tr>
                        <td class="p-2 border border-gray-300 text-center"></td>
                        <td class="p-2 border border-gray-300">OAT</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->oat, 'en') }}</td>
                    </tr>

                    <!-- total price -->
                    <tr class="font-bold bg-gray-100">
                        <td colspan="2" class="p-2 border border-gray-300">Total Quotation</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->price, 'en') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </section>

    {{-- Terms and Conditions --}}
    <section class="mb-8 text-sm">
        <h2 class="text-lg font-semibold mb-2">Terms and Conditions</h2>
        <ol class="list-none space-y-2">
            <li class="flex"><span class="mr-2">1.</span><span>This price quotation is valid for the period {{ $record->sph_date->format('F j, Y') }} - {{ $record->valid_until_date->format('F j, Y') }}.</span></li>
            <li class="flex"><span class="mr-2">2.</span><div><span>Payment of CASH invoice after documents are received via transfer to Bank BNI</span><div class="font-semibold ml-4">Account No: ********* On behalf of PT. Lintas Riau Prima</div></div></li>
            <li class="flex"><span class="mr-2">3.</span><span>We receive the PO at least 3 (three) days (via email or WA) before delivery.</span></li>
            <li class="flex"><span class="mr-2">4.</span><span>For urgent conditions, please coordinate directly before 12:00 PM on the same day.</span></li>
        </ol>
    </section>

    {{-- Signature Section --}}
    <section class="pt-8 flex justify-end">
        <div class="text-center">
            <p class="text-sm">Sincerely,</p>
            <p class="text-sm">PT Lintas Riau Prima</p>

            @if($signer)
                {{-- The signature mounter will now show the correct person's signature --}}
                <x-signature-mounter :user="$signer" />

                {{-- The name and position will also be from the correct person --}}
                <p class="text-sm font-bold underline">{{ $signer->name ?? 'No Name' }}</p>
                <p class="text-xs text-gray-600">{{ $signer->getPosition('en') ?? 'No Position' }}</p>
            @else
                {{-- Fallback content if no signer could be determined --}}
                <div style="height: 100px;"></div>
                <p class="text-sm font-bold underline">No Data</p>
                <p class="text-xs text-gray-600">No Data</p>
            @endif
        </div>
    </section>

    {{-- Footer Section --}}
    <footer class="mt-16 pt-4 border-t-4 border-blue-800 flex justify-between items-center text-xs">
        <div class="flex items-center space-x-2">
            @if(isset($isoCertifications))
                @foreach($isoCertifications as $cert)
                    <img src="{{ $cert->logo_url }}" alt="{{ $cert->name }}" class="h-10">
                @endforeach
            @endif
        </div>
        <div class="text-center">
            <p class="font-bold">PT. LINTAS RIAU PRIMA</p>
            {{-- DYNAMIC: Address from letter setting --}}
            <p>{{ $letterSetting?->address }}</p>
        </div>
        <div class="text-left">
            {{-- UPDATED: This section is now static --}}
            <p>☎️ 0761-22369</p>
            <p>✉️ <EMAIL></p>
            <p>🌐 www.lintasriauprima.com</p>
        </div>
    </footer>
</div>
