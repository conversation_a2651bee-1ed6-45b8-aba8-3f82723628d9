<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReceiptResource\Pages;
use App\Models\Receipt;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\File;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;

class ReceiptResource extends Resource
{
    protected static ?string $model = Receipt::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?string $navigationLabel = 'Receipt';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Receipt')
                    ->schema([
                        Forms\Components\TextInput::make('nomor_receipt')
                            ->label('Nomor Receipt')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(100),

                        Forms\Components\Select::make('id_invoice')
                            ->label('Invoice')
                            ->relationship('invoice', 'nomor_invoice')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('id_do')
                            ->label('Delivery Order')
                            ->relationship('deliveryOrder', 'kode')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\DateTimePicker::make('tanggal_receipt')
                            ->label('Tanggal Receipt')
                            ->required()
                            ->default(now()),

                        Forms\Components\DateTimePicker::make('tanggal_pembayaran')
                            ->label('Tanggal Pembayaran')
                            ->required()
                            ->default(now()),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'confirmed' => 'Dikonfirmasi',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->required()
                            ->default('pending'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Informasi Pembayaran')
                    ->schema([
                        Forms\Components\Select::make('metode_pembayaran')
                            ->label('Metode Pembayaran')
                            ->options([
                                'cash' => 'Tunai',
                                'transfer' => 'Transfer Bank',
                                'check' => 'Cek',
                                'giro' => 'Giro',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('referensi_pembayaran')
                            ->label('Referensi Pembayaran')
                            ->maxLength(100),

                        Forms\Components\TextInput::make('jumlah_pembayaran')
                            ->label('Jumlah Pembayaran')
                            ->required()
                            ->numeric()
                            ->prefix('Rp'),

                        Forms\Components\TextInput::make('biaya_admin')
                            ->label('Biaya Admin')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),

                        Forms\Components\TextInput::make('total_diterima')
                            ->label('Total Diterima')
                            ->required()
                            ->numeric()
                            ->prefix('Rp'),

                        Forms\Components\TextInput::make('bank_pengirim')
                            ->label('Bank Pengirim')
                            ->maxLength(100),

                        Forms\Components\TextInput::make('bank_penerima')
                            ->label('Bank Penerima')
                            ->maxLength(100),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Catatan')
                    ->schema([
                        Forms\Components\Textarea::make('catatan')
                            ->label('Catatan')
                            ->rows(3),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomor_receipt')
                    ->label('Nomor Receipt')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('invoice.nomor_invoice')
                    ->label('Invoice')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('deliveryOrder.kode')
                    ->label('Delivery Order')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_receipt')
                    ->label('Tanggal Receipt')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('metode_pembayaran')
                    ->label('Metode Pembayaran')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cash' => 'Tunai',
                        'transfer' => 'Transfer Bank',
                        'check' => 'Cek',
                        'giro' => 'Giro',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('jumlah_pembayaran')
                    ->label('Jumlah Pembayaran')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_diterima')
                    ->label('Total Diterima')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'confirmed',
                        'danger' => 'cancelled',
                    ])
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Pending',
                        'confirmed' => 'Dikonfirmasi',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Pending',
                        'confirmed' => 'Dikonfirmasi',
                        'cancelled' => 'Dibatalkan',
                    ]),

                Tables\Filters\SelectFilter::make('metode_pembayaran')
                    ->label('Metode Pembayaran')
                    ->options([
                        'cash' => 'Tunai',
                        'transfer' => 'Transfer Bank',
                        'check' => 'Cek',
                        'giro' => 'Giro',
                    ]),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('preview')
                    ->label('Preview')
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->action(null)
                    ->modalContent(function (Receipt $record): \Illuminate\View\View {
                        // Load the record with all necessary relationships
                        $record->load([
                            'invoice.transaksiPenjualan.pelanggan.alamatUtama',
                            'transaksiPenjualan.pelanggan.alamatUtama',
                            'deliveryOrder',
                            'createdBy.jabatan'
                        ]);

                        return \Illuminate\Support\Facades\View::make('receipt.receipt-preview', ['record' => $record]);
                    })
                    ->modalHeading(fn(Receipt $record) => "Preview: {$record->nomor_receipt}")
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Tutup')
                    ->slideOver()
                    ->modalWidth('4xl'),

                // PDF Receipt Indonesian
                Tables\Actions\Action::make('print_receipt_id')
                    ->label('Kwitansi (ID)')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->visible(fn(): bool => Auth::user()?->can('view', Receipt::class) ?? false)
                    ->action(function (Receipt $record) {
                        try {
                            // Load the receipt with all necessary relationships
                            $receipt = Receipt::with([
                                'invoice.transaksiPenjualan.pelanggan',
                                'transaksiPenjualan.pelanggan',
                                'deliveryOrder',
                                'createdBy'
                            ])->find($record->id);

                            if (!$receipt) {
                                throw new \Exception('Receipt not found');
                            }

                            // Generate dynamic filename
                            $filename = 'Kwitansi_' . str_replace(['/', '\\', ' '], '_', $receipt->nomor_receipt) . '_' . now()->format('Ymd_His') . '.pdf';

                            // Get logo as base64
                            $logoPath = public_path('images/lrp.png');
                            $logoBase64 = '';

                            if (File::exists($logoPath)) {
                                $logoBase64 = base64_encode(File::get($logoPath));
                            }

                            // Load the PDF view with the record data
                            $pdf = Pdf::loadView('pdf.receipt', [
                                'record' => $receipt,
                                'logoBase64' => $logoBase64
                            ])
                                ->setPaper('a4', 'portrait')
                                ->setOptions([
                                    'isHtml5ParserEnabled' => true,
                                    'isPhpEnabled' => true,
                                    'defaultFont' => 'Arial',
                                    'dpi' => 150,
                                    'defaultPaperSize' => 'a4',
                                    'chroot' => public_path(),
                                ]);

                            // Stream the PDF as a download response
                            return response()->streamDownload(fn() => print($pdf->output()), $filename);
                        } catch (\Throwable $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Error generating PDF')
                                ->body('Failed to generate receipt PDF: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            return null;
                        }
                    }),

                // PDF Receipt English
                Tables\Actions\Action::make('print_receipt_en')
                    ->label('Receipt (EN)')
                    ->icon('heroicon-o-printer')
                    ->color('info')
                    ->visible(fn(): bool => Auth::user()?->can('view', Receipt::class) ?? false)
                    ->action(function (Receipt $record) {
                        try {
                            // Load the receipt with all necessary relationships
                            $receipt = Receipt::with([
                                'invoice.transaksiPenjualan.pelanggan',
                                'transaksiPenjualan.pelanggan',
                                'deliveryOrder',
                                'createdBy'
                            ])->find($record->id);

                            if (!$receipt) {
                                throw new \Exception('Receipt not found');
                            }

                            // Generate dynamic filename
                            $filename = 'Receipt_EN_' . str_replace(['/', '\\', ' '], '_', $receipt->nomor_receipt) . '_' . now()->format('Ymd_His') . '.pdf';

                            // Get logo as base64
                            $logoPath = public_path('images/lrp.png');
                            $logoBase64 = '';

                            if (File::exists($logoPath)) {
                                $logoBase64 = base64_encode(File::get($logoPath));
                            }

                            // Load the PDF view with the record data
                            $pdf = Pdf::loadView('pdf.receipt_en', [
                                'record' => $receipt,
                                'logoBase64' => $logoBase64
                            ])
                                ->setPaper('a4', 'portrait')
                                ->setOptions([
                                    'isHtml5ParserEnabled' => true,
                                    'isPhpEnabled' => true,
                                    'defaultFont' => 'Arial',
                                    'dpi' => 150,
                                    'defaultPaperSize' => 'a4',
                                    'chroot' => public_path(),
                                ]);

                            // Stream the PDF as a download response
                            return response()->streamDownload(fn() => print($pdf->output()), $filename);
                        } catch (\Throwable $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Error generating PDF')
                                ->body('Failed to generate receipt PDF: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            return null;
                        }
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReceipts::route('/'),
            'create' => Pages\CreateReceipt::route('/create'),
            'view' => Pages\ViewReceipt::route('/{record}'),
            'edit' => Pages\EditReceipt::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
